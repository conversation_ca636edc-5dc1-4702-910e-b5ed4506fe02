/**
 * Express 应用配置
 * 配置中间件、路由和错误处理
 */

import express, { Application } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { createApiRouter } from './routes';
import { 
  errorHandler, 
  notFoundHandler, 
  timeoutHandler 
} from './middleware/error-handler';
import { 
  requestLogger, 
  performanceMonitor, 
  requestSizeMonitor,
  apiUsageStats 
} from './middleware/logger';

/**
 * 应用配置选项
 */
export interface AppConfig {
  /** 是否启用调试模式 */
  debug?: boolean;
  /** 请求超时时间（毫秒） */
  timeout?: number;
  /** 慢请求阈值（毫秒） */
  slowThreshold?: number;
  /** 大请求阈值（字节） */
  sizeThreshold?: number;
  /** CORS 配置 */
  cors?: {
    origin?: string | string[] | boolean;
    credentials?: boolean;
  };
}

/**
 * 创建 Express 应用
 * 
 * @param config 应用配置
 * @returns Express 应用实例
 */
export function createApp(config: AppConfig = {}): Application {
  const app = express();

  // 设置默认配置
  const {
    debug = false,
    timeout = 30000,
    slowThreshold = 1000,
    sizeThreshold = 1024 * 1024, // 1MB
    cors: corsConfig = {}
  } = config;

  console.log('🚀 正在初始化当贝AI Provider HTTP API服务器...');

  // 1. 安全中间件
  app.use(helmet({
    contentSecurityPolicy: false, // 禁用CSP以支持API调用
    crossOriginEmbedderPolicy: false // 禁用COEP以支持跨域
  }));

  // 2. CORS 中间件
  app.use(cors({
    origin: corsConfig.origin || '*',
    credentials: corsConfig.credentials || false,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'X-Request-ID'
    ],
    exposedHeaders: ['X-Request-ID']
  }));

  // 3. 请求解析中间件
  app.use(express.json({ 
    limit: '10mb',
    strict: true
  }));
  
  app.use(express.urlencoded({ 
    extended: true, 
    limit: '10mb' 
  }));

  // 4. 日志和监控中间件
  if (debug) {
    app.use(requestLogger);
  }
  
  app.use(performanceMonitor(slowThreshold));
  app.use(requestSizeMonitor(sizeThreshold));
  app.use(apiUsageStats.getMiddleware());

  // 5. 请求超时中间件
  app.use(timeoutHandler(timeout));

  // 6. 健康检查端点（在所有其他路由之前）
  app.get('/health', (_req, res) => {
    res.json({
      success: true,
      data: {
        status: 'healthy',
        timestamp: Date.now(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.env['npm_package_version'] || '1.0.0'
      }
    });
  });

  // 7. API 统计端点
  app.get('/stats', (_req, res) => {
    res.json({
      success: true,
      data: {
        usage: apiUsageStats.getStats(),
        system: {
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          cpu: process.cpuUsage(),
          version: process.version,
          platform: process.platform
        },
        timestamp: Date.now()
      }
    });
  });

  // 8. 主要 API 路由
  app.use('/api', createApiRouter());

  // 9. 根路径重定向到 API 信息
  app.get('/', (_req, res) => {
    res.redirect('/api/info');
  });

  // 10. 404 处理
  app.use(notFoundHandler);

  // 11. 全局错误处理
  app.use(errorHandler);

  console.log('✅ 当贝AI Provider HTTP API服务器初始化完成');
  console.log('📋 可用端点:');
  console.log('   GET  /health          - 健康检查');
  console.log('   GET  /stats           - 使用统计');
  console.log('   GET  /api/info        - API信息');
  console.log('   GET  /api/models      - 模型列表');
  console.log('   POST /api/chat        - 聊天对话');

  return app;
}

/**
 * 优雅关闭处理
 * 
 * @param server HTTP 服务器实例
 */
export function setupGracefulShutdown(server: any): void {
  const shutdown = (signal: string) => {
    console.log(`\n🛑 收到 ${signal} 信号，开始优雅关闭...`);
    
    server.close((err: any) => {
      if (err) {
        console.error('❌ 服务器关闭时出错:', err);
        process.exit(1);
      }
      
      console.log('✅ 服务器已优雅关闭');
      process.exit(0);
    });

    // 强制关闭超时
    setTimeout(() => {
      console.error('⏰ 强制关闭超时，立即退出');
      process.exit(1);
    }, 10000);
  };

  // 监听关闭信号
  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));

  // 监听未捕获的异常
  process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
    shutdown('uncaughtException');
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    console.error('Promise:', promise);
    shutdown('unhandledRejection');
  });
}
