/**
 * HTTP API 相关类型定义
 * 定义模型列表和聊天接口的请求响应格式
 */

/**
 * 标准 API 响应格式
 */
export interface ApiResponse<T = any> {
  /** 请求是否成功 */
  success: boolean;
  /** 响应数据 */
  data?: T;
  /** 错误信息 */
  error?: {
    /** 错误代码 */
    code: string;
    /** 错误消息 */
    message: string;
    /** 详细错误信息 */
    details?: any;
  };
  /** 请求ID */
  requestId?: string;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 模型选项配置
 */
export interface ModelOption {
  /** 选项名称 */
  name: string;
  /** 选项值 */
  value: string;
  /** 是否启用 */
  enabled: boolean;
  /** 是否默认选中 */
  selected?: boolean;
}

/**
 * 模型信息
 */
export interface ModelInfo {
  /** 模型唯一标识符 */
  id: string;
  /** 模型显示名称 */
  name: string;
  /** 模型描述 */
  description: string;
  /** 支持的选项 */
  options: ModelOption[];
  /** 是否推荐 */
  recommended: boolean;
  /** 是否置顶 */
  pinned: boolean;
  /** 图标URL */
  icon?: string;
  /** 横幅URL */
  banner?: string;
  /** 徽章文本 */
  badge?: string;
}

/**
 * 模型列表响应
 */
export interface ModelsResponse {
  /** 默认模型 */
  defaultModel: string;
  /** 模型列表 */
  models: ModelInfo[];
  /** 模型总数 */
  total: number;
}

/**
 * 聊天消息
 */
export interface ChatMessage {
  /** 消息角色 */
  role: 'user' | 'assistant' | 'system';
  /** 消息内容 */
  content: string;
  /** 消息ID（可选） */
  id?: string;
  /** 时间戳（可选） */
  timestamp?: number;
}

/**
 * 聊天请求参数
 */
export interface ChatRequest {
  /** 消息列表 */
  messages: ChatMessage[];
  /** 使用的模型 */
  model: string;
  /** 是否启用流式响应 */
  stream?: boolean;
  /** 对话ID（可选，如果不提供会自动创建） */
  conversation_id?: string;
  /** 最大令牌数（可选） */
  max_tokens?: number;
  /** 温度参数（可选） */
  temperature?: number;
  /** 聊天选项（可选） */
  options?: {
    /** 是否启用深度思考 */
    deep_thinking?: boolean;
    /** 是否启用联网搜索 */
    online_search?: boolean;
  };
}

/**
 * 聊天响应（非流式）
 */
export interface ChatResponse {
  /** 响应消息 */
  message: ChatMessage;
  /** 对话ID */
  conversation_id: string;
  /** 消息ID */
  message_id: string;
  /** 父消息ID */
  parent_message_id?: string;
  /** 请求ID */
  request_id: string;
  /** 使用的模型 */
  model: string;
  /** 完成原因 */
  finish_reason?: 'stop' | 'length' | 'error';
  /** 使用统计 */
  usage?: {
    /** 提示令牌数 */
    prompt_tokens: number;
    /** 完成令牌数 */
    completion_tokens: number;
    /** 总令牌数 */
    total_tokens: number;
  };
}

/**
 * 流式聊天响应数据块
 */
export interface ChatStreamChunk {
  /** 数据块ID */
  id: string;
  /** 对象类型 */
  object: 'chat.completion.chunk';
  /** 创建时间 */
  created: number;
  /** 使用的模型 */
  model: string;
  /** 选择列表 */
  choices: Array<{
    /** 选择索引 */
    index: number;
    /** 增量消息 */
    delta: {
      /** 角色（仅第一个块包含） */
      role?: 'assistant';
      /** 内容增量 */
      content?: string;
    };
    /** 完成原因 */
    finish_reason?: 'stop' | 'length' | 'error' | null;
  }>;
}

/**
 * 错误响应
 */
export interface ErrorResponse {
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 错误类型 */
  type: 'invalid_request' | 'authentication_error' | 'rate_limit_error' | 'server_error';
  /** 详细信息 */
  details?: any;
}
